import { queryMerchantBusinessNews, queryShopOverviewData, queryShopDetailData } from '@/services';
import { useRequest, useSize } from 'ahooks';
import { Button, FormInstance, Result, Spin, Form } from 'antd';
import styled from 'styled-components';
import { useDataState } from './components/store/useDataState';
import { trace } from '@/utils/trace';
import { inPuppeteerCtx } from './components/utils';
import emitter from '@/utils/emitters';
import { useEffect, useRef } from 'react';
import DownloadImg from './components/download-img';
import { FilterBar } from './components/filter-bar';
import { ITraceData, NewBusinessNewsType } from './components/const';
import { getFoodData, getHiddenKeys, getOtherData } from './components/handle-data';
import RightTargetData from './components/right-target-data';
import { definePageConfig } from 'ice';

const Container = styled.div`
  border-radius: 4px;
  min-height: 100%;
  position: relative;
  background: #f6f7f8;
  padding-bottom: 1.2em;
`;
interface IProps {
  form: FormInstance;
  hasOptGroup: boolean;
  summaryTemplate: string;
  sendReport: any;
  traceData?: ITraceData;
  merchantName: string;
  traceError?: any;
}
export default function BusinessNews(props: IProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const size = useSize(containerRef);
  const fontSize = size?.width ? size.width / 40 : 10;
  const { hasOptGroup, summaryTemplate, form, sendReport, traceData, merchantName, traceError } =
    props;
  const {
    dateRange,
    shopIdList,
    hiddenFields,
    pid,
    aiSummary,
    shopList,
    isChild,
    setIsFood,
    isMultiShops,
  } = useDataState();
  const allShopsSelected = Form.useWatch('allShopsSelected', form) || false;

  const {
    error,
    data: businessData,
    loading,
    run: queryData,
    mutate: setGoodsNewsInfo,
  } = useRequest(
    async () => {
      const startDate = dateRange?.[0];
      const endDate = dateRange?.[1];
      if (!startDate && !endDate) {
        return Promise.reject(new Error('未选择时间'));
      }
      await form.validateFields();
      if (!startDate && !endDate) {
        return;
      }
      // 如果没有选择"户下全部门店"且没有选择具体门店，则报错
      if (!allShopsSelected && !shopIdList?.length) {
        return Promise.reject(new Error('未选择门店'));
      }
      // 当选择"户下全部门店"时，不传递shopIdList，只传递pid
      const params: any = {
        startDate,
        endDate,
        pid,
      };

      // 只有在没有选择"户下全部门店"时才传递shopIdList
      if (!allShopsSelected) {
        params.shopIdList = shopIdList;
      }
      if (isMultiShops) {
        aiSummary.endInterval();
      }
      const isAnnual = endDate.diff(startDate, 'day') > 62;
      const baseData = await queryMerchantBusinessNews(params);

      const [overviewRes, detailRes] = await Promise.all([
        queryShopOverviewData(params),
        queryShopDetailData(params),
      ]);
      // 门店汇总数据（右边数据）
      const overviewData = overviewRes.applicationDataList?.[0];
      const overviewRuleDataList = overviewData?.ruleDataList || [];
      const data = overviewRuleDataList[0]?.values?.[0] || {};
      console.log(data, 'data');

      // 门店明细数据（左边门店明细列表）
      const detailData = detailRes.applicationDataList?.[0];
      const detailRuleDataList = detailData?.ruleDataList || [];
      console.log('detailRes', detailRes);

      const shopDetail = detailRuleDataList[0]?.values || [];
      const specialValues = overviewData?.ruleDataList[0]?.specialValues || [];
      let businessNewType = specialValues.find((item) => item.key === 'businessNewType')?.keyValue;

      // 如果 specialValues 中没有 businessNewType，则根据 data 中的 xibao_version_name 判断
      if (!businessNewType && data.xibao_version_name === 'FOOD') {
        businessNewType = NewBusinessNewsType.FOOD;
      }

      const isFood = businessNewType === NewBusinessNewsType.FOOD;
      console.log(
        'businessNewType:',
        businessNewType,
        'isFood:',
        isFood,
        'xibao_version_name:',
        data.xibao_version_name,
      );
      console.log('原始数据 data:', data);
      console.log('门店明细数据 shopDetail:', shopDetail);
      setIsFood(isFood);
      if (!isMultiShops) {
        aiSummary.createAiSummary();
      }
      const hasAiData =
        specialValues.find((item) => item.key === 'is_aiagent_online')?.keyValue === '1';
      console.log('hasAiData:', hasAiData, 'specialValues:', specialValues);
      let fields = isFood
        ? getFoodData(data, {
            hasAiData,
          })
        : getOtherData(data, {
            hasAiData,
          });
      console.log('处理后的 fields:', fields);
      const isRecharge = !!baseData?.recharge;
      if (!isRecharge) {
        fields = fields.filter((item) => !['广告数据', '广告投放'].includes(item.label));
      }
      traceData?.(
        {
          businessNewType,
          data: {
            ...baseData,
            dataResult: data,
          },
          formData: params,
        },
        {
          shopData: shopDetail,
          fields,
        },
      );
      return {
        ...baseData,
        businessNewType: businessNewType || NewBusinessNewsType.OTHER,
        dataResult: data,
        recharge: isRecharge,
        fields,
      };
    },
    {
      manual: true,
      debounceWait: 1000,
      debounceLeading: false,
      debounceTrailing: true,
    },
  );

  /**
   * node端截图时请求喜报数据,手动渲染
   */
  const queryDataInBack = async () => {
    const { pageData, url } = window.refreshPage?.();
    trace('nodeScreenShot', { pageData, url });
    if (pageData && url) {
      const isFood = pageData?.businessNewType === NewBusinessNewsType.FOOD;
      let fields = isFood
        ? getFoodData(pageData?.dataResult, {
            hasAiData: pageData?.isAiAgentOnline === '1',
            isNode: true,
          })
        : getOtherData(pageData?.dataResult, {
            hasAiData: pageData?.isAiAgentOnline === '1',
          });
      const isRecharge = !!pageData?.recharge;
      if (!isRecharge) {
        fields = fields.filter((item) => !['广告数据', '广告投放'].includes(item.label));
      }
      const _hiddenFields = getHiddenKeys(isFood);
      setGoodsNewsInfo({ ...pageData, fields, hiddenFields: _hiddenFields });
      // eslint-disable-next-line @typescript-eslint/no-shadow
      window.setInvokeFlag?.(true);
      trace('invoke');
    }
  };
  const renderInPuppeteer = () => {
    emitter.on('updatePageInfo', queryDataInBack);
    queryDataInBack();
  };

  // 监听puppeteer推送消息
  useEffect(() => {
    if (inPuppeteerCtx()) {
      trace('nodeInit');
      // 来自puppeteer渲染
      renderInPuppeteer();
    }
  }, []);
  useEffect(() => {
    if (isChild) {
      queryData();
    }
  }, [dateRange, shopList, pid]);
  const errorMessage = error?.message || error?.errorFields?.[0]?.errors?.[0];
  useEffect(() => {
    traceError?.(error);
  }, [error]);

  const PlaceHolder = (
    <Result
      style={{ padding: '1em 0 4em', margin: '0 2em' }}
      title={<div style={{ wordBreak: 'break-all' }}>{errorMessage || '数据拉取中...'}</div>}
      status={errorMessage ? 'warning' : 'info'}
      extra={
        errorMessage ? (
          <Button type="primary" onClick={queryData}>
            点击重试
          </Button>
        ) : null
      }
    />
  );

  return (
    <Spin spinning={loading}>
      <Container
        id="downloadImg"
        ref={containerRef}
        style={{ fontSize: fontSize > 16 ? 16 : fontSize }}
      >
        <FilterBar date={dateRange} />
        {errorMessage || !businessData ? (
          PlaceHolder
        ) : (
          <RightTargetData
            data={businessData}
            summaryTemplate={summaryTemplate}
            // @ts-ignore 兼容字段结构
            hiddenFields={businessData?.hiddenFields || hiddenFields}
            shopIdList={shopIdList}
            dateRange={dateRange}
          />
        )}
      </Container>
      {isChild && !errorMessage && businessData ? (
        <DownloadImg
          isMultiShops={isMultiShops}
          name={merchantName}
          hasOptGroup={hasOptGroup}
          pid={pid}
          sendReport={sendReport}
        />
      ) : null}
    </Spin>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '门店经营喜报',
  spm: {
    spmB: 'business-news',
  },
}));
