/* eslint-disable no-nested-ternary */
import { Flex } from 'antd';
import styled from 'styled-components';
import { useDataState } from '../store/useDataState';
import { useEffect } from 'react';
import { formatterTargetValue } from '../utils';

const Container = styled.div`
  border-radius: 1.2em;
  margin-bottom: 1.4em;
`;

const getLevelIcon = (scoreLevel: string) => {
  const levelIcons: Record<string, string> = {
    LV1: 'https://img.alicdn.com/imgextra/i2/O1CN01YFO3Fq1OMzEOTvAHB_!!6000000001692-2-tps-63-63.png',
    LV2: 'https://img.alicdn.com/imgextra/i2/O1CN01hmiRG721NESSinYXk_!!6000000006972-2-tps-63-63.png',
    LV3: 'https://img.alicdn.com/imgextra/i2/O1CN01MEjBWI1pxA8laMQtx_!!6000000005426-2-tps-63-63.png',
    LV4: 'https://img.alicdn.com/imgextra/i2/O1CN01ZZHMd61hZKbd99t7t_!!6000000004291-2-tps-63-63.png',
    LV5: 'https://img.alicdn.com/imgextra/i1/O1CN01pjeChy1z8SRxDXP2n_!!6000000006669-2-tps-63-63.png',
    LV6: 'https://img.alicdn.com/imgextra/i4/O1CN01P3MInS1oUw7DZo6HH_!!6000000005229-2-tps-63-63.png',
  };
  return (
    levelIcons[scoreLevel] ||
    'https://img.alicdn.com/imgextra/i1/O1CN01LQl4h71QRgULauXwn_!!6000000001973-2-tps-156-156.png'
  ); // 默认返回 原图标
};

const ContainerHeader = styled.div`
  border-radius: 1.2em 1.2em 0 0;
  padding: 0 1.4em;
  background: linear-gradient(90deg, #ffedae 0%, #fffbeb 100%);
  box-shadow: 0px 10px 30px 0px rgba(253, 230, 142, 0.2);
  height: 6em;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
const ContainerContent = styled.div`
  background: #fff;
  border-radius: 1.2em;
  padding: 1.4em;
  margin-top: -0.5em;
`;
const TargetItemWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.7em;
`;
const TipWrapper = styled.div`
  border-radius: 1.6em;
  padding: 1.4em;
  background: #f6f7f8;
  margin-top: 1em;
`;

const TargetItem = (props: { title: string; value: string; icon: string; unit?: string }) => {
  const { title, value, icon, unit } = props;

  return (
    <Flex align="center">
      <img src={icon} style={{ width: '3.4em', height: '3.4em', marginRight: '0.8em' }} />
      <div>
        <div style={{ fontSize: '1.2em', color: '#3D3D3D' }}>
          {title} {unit && <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>({unit})</span>}
        </div>
        <div style={{ fontSize: '1.6em', fontWeight: 600, color: 'rgba(0, 0, 0, 0.9)' }}>
          {value}
        </div>
      </div>
    </Flex>
  );
};

export default function AnnualBenefits(props: { data: any }) {
  const { data = {} } = props;
  const { isShow, registerField, unregisterField } = useDataState();
  const getValue = (key: string) => {
    return formatterTargetValue(data[key]) || '-';
  };
  const targetList = [
    {
      title: '防干扰',
      icon: 'https://img.alicdn.com/imgextra/i1/O1CN01bt8OXj22j2txAQK9b_!!6000000007155-2-tps-136-136.png',
      unit: '天',
      value: getValue('wp_online_days'),
    },
    {
      title: '累计年费在约',
      icon: 'https://img.alicdn.com/imgextra/i3/O1CN01Lh86Eq1P5Pc2P1kLv_!!6000000001789-2-tps-136-136.png',
      unit: '天',
      value: getValue('wp_online_days_total'),
    },
    // {
    //   title: '防截流',
    //   icon: 'https://img.alicdn.com/imgextra/i2/O1CN01ZpcVzy1UDGTxKo9q7_!!6000000002483-2-tps-136-136.png',
    //   unit: '次',
    //   value: getValue('anti_hijacking_cnt'),
    // },
    {
      title: '信息安全守护',
      unit: '次',
      icon: 'https://img.alicdn.com/imgextra/i2/O1CN01lnsYtu1cR1IsgmsBA_!!6000000003596-2-tps-136-136.png',
      value: getValue('security_guards_cnt'),
    },
  ];
  useEffect(() => {
    registerField({
      key: 'annualBenefits',
      label: '门店信息守护',
      children: targetList.map((item) => ({
        ...item,
        key: item.title,
      })),
    });
    return () => {
      unregisterField('annualBenefits');
    };
  }, []);
  const fields = isShow(targetList.map((item) => item.title));
  if (fields.every((item) => !item)) {
    return null;
  }
  console.log(getLevelIcon(data.score_level), 'getLevelIcon(data.score_level)');

  return (
    <Container>
      <ContainerHeader>
        <div style={{ fontSize: '1.8em', color: '#663400' }}>门店信息守护</div>
        <img src={getLevelIcon(data.score_level)} style={{ width: '3.9em', height: '3.9em' }} />
        <img
          src="https://img.alicdn.com/imgextra/i1/O1CN01Zm501T1moVm9ZTIs3_!!6000000005001-2-tps-1077-174.png"
          style={{ position: 'absolute', left: 0, top: 0, height: '100%' }}
        />
      </ContainerHeader>
      <ContainerContent>
        <TargetItemWrapper>
          {targetList
            .filter((_, index) => fields[index])
            .map((item) => (
              <TargetItem key={item.title} {...item} />
            ))}
        </TargetItemWrapper>
        <TipWrapper>
          {data.is_wp_online === '1' ? (
            <>
              <div style={{ fontSize: '1.2em', fontWeight: 600 }}>
                高德全力守护您的信息防止他人恶意修改，您的权益剩余
                <span style={{ color: '#FF5E33' }}>{data.wp_online_remaining_days}天</span>
                ，请及时关注续费
              </div>
            </>
          ) : (
            <>
              <div style={{ fontSize: '1.2em', fontWeight: 600 }}>
                高德全力守护您的信息防止他人恶意修改，您的权益已到期
                <span style={{ color: '#FF5E33' }}>{Math.abs(data.wp_offline_days)}天</span>
                ，请及时续费
              </div>
            </>
          )}
        </TipWrapper>
      </ContainerContent>
    </Container>
  );
}
